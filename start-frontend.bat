@echo off
echo Starting Frontend Vue.js Application...
echo.

cd /d "%~dp0app"

echo Checking if dependencies are installed...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Failed to install dependencies!
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
    echo.
)

echo Starting development server...
echo Frontend application will run on http://localhost:5173
echo Press Ctrl+C to stop the service
echo.

npm run dev

pause 