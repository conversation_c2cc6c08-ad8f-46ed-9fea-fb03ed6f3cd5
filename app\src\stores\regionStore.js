import { defineStore } from 'pinia'
import { userService } from '../services/userService'

/**
 * 地区数据管理 Store
 * 负责管理省市数据的缓存和加载
 */
export const useRegionStore = defineStore('region', {
  state: () => ({
    // 省份数据
    provinces: [],
    // 城市数据映射 { provinceCode: [cities] }
    citiesMap: {},
    // 加载状态
    loading: false,
    // 错误信息
    error: null,
    // 数据是否已初始化
    initialized: false,
    // 最后更新时间
    lastUpdated: null
  }),

  getters: {
    /**
     * 获取所有省份
     */
    getAllProvinces: (state) => state.provinces,

    /**
     * 根据省份代码获取城市列表
     */
    getCitiesByProvince: (state) => (provinceCode) => {
      return state.citiesMap[provinceCode] || []
    },

    /**
     * 根据省份代码获取省份名称
     */
    getProvinceName: (state) => (provinceCode) => {
      const province = state.provinces.find(p => p.code === provinceCode)
      return province ? province.name : ''
    },

    /**
     * 根据城市代码获取城市名称
     */
    getCityName: (state) => (provinceCode, cityCode) => {
      const cities = state.citiesMap[provinceCode] || []
      const city = cities.find(c => c.code === cityCode)
      return city ? city.name : ''
    },

    /**
     * 格式化地区字符串
     */
    formatRegionString: (state) => (provinceCode, cityCode) => {
      const provinceName = state.provinces.find(p => p.code === provinceCode)?.name || ''
      const cityName = state.citiesMap[provinceCode]?.find(c => c.code === cityCode)?.name || ''
      
      if (provinceName && cityName) {
        return `${provinceName} ${cityName}`
      } else if (provinceName) {
        return provinceName
      }
      return ''
    },

    /**
     * 检查数据是否需要刷新（超过1小时）
     */
    needsRefresh: (state) => {
      if (!state.lastUpdated) return true
      const oneHour = 60 * 60 * 1000
      return Date.now() - state.lastUpdated > oneHour
    }
  },

  actions: {
    /**
     * 初始化地区数据
     */
    async initRegionData() {
      // 如果已经初始化且不需要刷新，直接返回
      if (this.initialized && !this.needsRefresh) {
        console.log('🌍 地区数据已初始化，跳过重复加载')
        return
      }

      // 如果正在加载中，等待当前加载完成
      if (this.loading) {
        console.log('🌍 地区数据正在加载中，等待完成...')
        // 等待加载完成
        while (this.loading) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
        return
      }

      this.loading = true
      this.error = null

      try {
        console.log('🌍 正在加载地区数据...')
        const response = await userService.getRegions()

        // 检查响应格式
        const data = response && response.data ? response.data : response

        if (data && data.provinces && data.cities) {
          this.provinces = data.provinces
          this.citiesMap = data.cities
          this.initialized = true
          this.lastUpdated = Date.now()
          console.log('✅ 地区数据加载完成')
        } else {
          throw new Error('地区数据格式错误')
        }
      } catch (error) {
        this.error = error.message || '加载地区数据失败'
        console.error('❌ 地区数据加载失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 加载指定省份的城市数据
     */
    async loadCities(provinceCode) {
      if (!provinceCode) return []

      // 如果已经有缓存，直接返回
      if (this.citiesMap[provinceCode]) {
        return this.citiesMap[provinceCode]
      }

      try {
        console.log(`🏙️ 正在加载 ${provinceCode} 的城市数据...`)
        const response = await userService.getRegions(provinceCode)
        
        // 检查响应格式
        const data = response && response.data ? response.data : response
        
        if (data && Array.isArray(data)) {
          // 更新缓存
          this.citiesMap[provinceCode] = data
          console.log(`✅ ${provinceCode} 城市数据加载完成`)
          return data
        } else {
          console.warn(`⚠️ ${provinceCode} 城市数据格式错误`)
          return []
        }
      } catch (error) {
        console.error(`❌ 加载 ${provinceCode} 城市数据失败:`, error)
        return []
      }
    },

    /**
     * 清理地区数据
     */
    clearRegionData() {
      this.provinces = []
      this.citiesMap = {}
      this.initialized = false
      this.lastUpdated = null
      this.error = null
      console.log('🧹 地区数据已清理')
    },

    /**
     * 重新加载地区数据
     */
    async refreshRegionData() {
      this.initialized = false
      this.lastUpdated = null
      await this.initRegionData()
    },

    /**
     * 预加载热门城市数据
     */
    async preloadHotCities() {
      // 热门省份代码列表
      const hotProvinces = ['11', '31', '44', '32', '33', '35', '37', '42', '51', '50']
      
      try {
        console.log('🔥 开始预加载热门城市数据...')
        const promises = hotProvinces.map(provinceCode => 
          this.loadCities(provinceCode).catch(error => {
            console.warn(`预加载 ${provinceCode} 失败:`, error)
            return []
          })
        )
        
        await Promise.all(promises)
        console.log('✅ 热门城市数据预加载完成')
      } catch (error) {
        console.error('❌ 预加载热门城市数据失败:', error)
      }
    },

    /**
     * 搜索地区
     */
    searchRegions(keyword) {
      if (!keyword) return { provinces: [], cities: [] }

      const keyword_lower = keyword.toLowerCase()
      
      // 搜索省份
      const matchedProvinces = this.provinces.filter(province => 
        province.name.toLowerCase().includes(keyword_lower) ||
        province.code.includes(keyword)
      )

      // 搜索城市
      const matchedCities = []
      Object.entries(this.citiesMap).forEach(([provinceCode, cities]) => {
        const provinceName = this.getProvinceName(provinceCode)
        cities.forEach(city => {
          if (city.name.toLowerCase().includes(keyword_lower) ||
              city.code.includes(keyword)) {
            matchedCities.push({
              ...city,
              provinceName,
              provinceCode
            })
          }
        })
      })

      return {
        provinces: matchedProvinces,
        cities: matchedCities
      }
    }
  }
})
