import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import VueLazyload from 'vue-lazyload'
import ToastPlugin from './plugins/toast'
import config from './config'
import appInitializer from './utils/appInit'
import './assets/styles/index.css'

/**
 * 创建Vue应用实例
 */
const createVueApp = () => {
  const app = createApp(App)
  const pinia = createPinia()

  // 注册核心插件
  app.use(pinia)
  app.use(router)
  app.use(ToastPlugin)

  // 配置图片懒加载
  app.use(VueLazyload, {
    preLoad: 1.3,
    error: config.DEFAULT_AVATAR || '/default.png',
    loading: config.DEFAULT_AVATAR || '/default.png',
    attempt: 1,
    listenEvents: ['scroll', 'wheel', 'mousewheel', 'resize', 'animationend', 'transitionend', 'touchmove'],
    adapter: {
      loaded() {
        // 图片加载完成
      },
      loading() {
        // 图片加载中
      },
      error({ src }) {
        console.warn('图片加载失败:', src)
      }
    }
  })

  // Vue应用级错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue应用错误:', err)
    console.error('错误信息:', info)

    if (config.ENV === 'development') {
      console.error('组件实例:', vm)
    }
  }

  // Vue警告处理
  app.config.warnHandler = (msg, _vm, trace) => {
    if (config.ENV === 'development') {
      console.warn('Vue警告:', msg)
      console.warn('组件追踪:', trace)
    }
  }

  return app
}

/**
 * 应用启动主函数
 */
const startApp = async () => {
  try {
    console.log('🔄 正在启动应用...')

    // 1. 创建Vue应用
    const app = createVueApp()

    // 2. 挂载应用到DOM
    app.mount('#app')
    console.log('📱 Vue应用挂载完成')

    // 3. 执行应用初始化（异步，不阻塞应用渲染）
    appInitializer.initialize().catch(error => {
      console.error('❌ 应用初始化失败:', error)
    })

  } catch (error) {
    console.error('❌ 应用启动失败:', error)

    // 显示错误页面或提示
    document.body.innerHTML = `
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-family: Arial, sans-serif;
        background: #f5f5f5;
      ">
        <div style="text-align: center; padding: 20px;">
          <h2 style="color: #ff6b6b; margin-bottom: 10px;">应用启动失败</h2>
          <p style="color: #666; margin-bottom: 20px;">请刷新页面重试</p>
          <button
            onclick="location.reload()"
            style="
              background: #ff6b6b;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 5px;
              cursor: pointer;
            "
          >
            刷新页面
          </button>
        </div>
      </div>
    `
  }
}

// 启动应用
startApp()
