@echo off
echo Starting Backend Node.js Server...
echo.

cd /d "%~dp0serve"

echo Checking if dependencies are installed...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Failed to install dependencies!
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
    echo.
)

echo Starting server...
echo Backend API server will run on configured port
echo Press Ctrl+C to stop the service
echo.

npm run dev

pause 