<script>
import { watch, onUnmounted } from 'vue'
import { useMessageStore } from './stores/messageStore'
import { useUserStore } from './stores/userStore'
import { useRegionStore } from './stores/regionStore'
import appInitializer from './utils/appInit'

export default {
  name: 'App',
  setup() {
    const messageStore = useMessageStore()
    const userStore = useUserStore()
    const regionStore = useRegionStore()

    // 清理用户数据
    const cleanupUserData = () => {
      try {
        console.log('👋 用户登出，清理数据...')

        // 断开 Socket 连接
        messageStore.disconnectSocket()

        // 清理地区数据缓存
        regionStore.clearRegionData()

        // 通知应用初始化器用户已登出
        appInitializer.handleUserLogout()

        console.log('🧹 用户数据清理完成')
      } catch (error) {
        console.error('❌ 用户数据清理失败:', error)
      }
    }

    // 监听用户登录状态变化
    watch(() => userStore.isLogin, async (isLogin, wasLogin) => {
      if (isLogin && !wasLogin) {
        // 用户刚登录 - 触发应用初始化器处理
        console.log('👤 检测到用户登录，触发初始化...')
        appInitializer.handleUserLogin()

      } else if (!isLogin && wasLogin) {
        // 用户刚登出
        cleanupUserData()
      }
    }, { immediate: true })

    // 组件卸载时清理
    onUnmounted(() => {
      cleanupUserData()
    })

    // 提供给子组件使用
    return {
      regionStore
    }
  }
}
</script>

<template>
  <router-view v-slot="{ Component }">
    <transition name="fade" mode="out-in">
      <component :is="Component" :key="$route.path" />
    </transition>
  </router-view>
</template>

<style>
  #app {
    width: 100%;
    height: 100%;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
</style>
