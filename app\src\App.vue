<script>
import { watch, onUnmounted } from 'vue'
import { useMessageStore } from './stores/messageStore'
import { useUserStore } from './stores/userStore'
import { useRegionStore } from './stores/regionStore'

export default {
  name: 'App',
  setup() {
    const messageStore = useMessageStore()
    const userStore = useUserStore()
    const regionStore = useRegionStore()

    // 初始化用户相关数据
    const initUserData = async (userData) => {
      try {
        console.log('👤 用户登录，初始化数据...')

        // 初始化消息系统
        messageStore.init(userData)

        // 异步初始化消息数据（不阻塞主流程）
        messageStore.initData().catch(error => {
          console.warn('⚠️ 消息数据初始化失败:', error)
        })

        // 初始化地区数据
        await regionStore.initRegionData()

        // 异步预加载热门城市数据（不阻塞主流程）
        regionStore.preloadHotCities().catch(error => {
          console.warn('⚠️ 热门城市数据预加载失败:', error)
        })

        console.log('✅ 用户数据初始化完成')

      } catch (error) {
        console.error('❌ 用户数据初始化失败:', error)
      }
    }

    // 清理用户数据
    const cleanupUserData = () => {
      try {
        console.log('👋 用户登出，清理数据...')

        // 断开 Socket 连接
        messageStore.disconnectSocket()

        // 清理地区数据缓存
        regionStore.clearRegionData()

        console.log('🧹 用户数据清理完成')
      } catch (error) {
        console.error('❌ 用户数据清理失败:', error)
      }
    }

    // 监听用户登录状态变化
    watch(() => userStore.isLogin, async (isLogin, wasLogin) => {
      if (isLogin && !wasLogin) {
        // 用户刚登录
        const userData = {
          id: userStore.userInfo.id,
          name: userStore.userInfo.nickname || userStore.userInfo.username,
          avatar: userStore.userInfo.avatar,
          token: userStore.token
        }

        await initUserData(userData)

      } else if (!isLogin && wasLogin) {
        // 用户刚登出
        cleanupUserData()
      }
    }, { immediate: true })

    // 组件卸载时清理
    onUnmounted(() => {
      cleanupUserData()
    })

    // 提供给子组件使用
    return {
      regionStore
    }
  }
}
</script>

<template>
  <router-view v-slot="{ Component }">
    <transition name="fade" mode="out-in">
      <component :is="Component" :key="$route.path" />
    </transition>
  </router-view>
</template>

<style>
  #app {
    width: 100%;
    height: 100%;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
</style>
