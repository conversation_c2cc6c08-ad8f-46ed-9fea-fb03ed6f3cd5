# Project Startup Guide

## Startup Scripts

This project includes three startup scripts for convenient launching of frontend and backend services:

### 1. start-frontend.bat
- **Function**: Start the frontend Vue.js application
- **Port**: http://localhost:5173
- **Description**: Automatically checks and installs dependencies, then starts the development server

### 2. start-backend.bat
- **Function**: Start the backend Node.js server
- **Description**: Automatically checks and installs dependencies, then starts the API server

### 3. start-all.bat
- **Function**: Start both frontend and backend services simultaneously
- **Description**: Opens two command line windows, running frontend and backend services separately

## Usage

### Method 1: Start Individually
1. Double-click `start-backend.bat` to start the backend server
2. Double-click `start-frontend.bat` to start the frontend application

### Method 2: One-Click Startup
Double-click `start-all.bat` to start both frontend and backend simultaneously

## Important Notes

1. **First Run**: Scripts will automatically check and install npm dependencies, first run may take longer
2. **Ports**: Ensure related ports are not occupied by other applications
3. **Stop Services**: Press `Ctrl+C` in the corresponding command line window to stop services
4. **Node.js Version**: Ensure Node.js >= 16.0.0 is installed

## Project Structure

- `app/` - Frontend Vue.js application
- `serve/` - Backend Node.js API server

## Development Mode

All startup scripts run in development mode:
- Frontend: Uses Vite development server with hot reload
- Backend: Uses nodemon to monitor file changes and auto-restart server

## Troubleshooting

If you encounter problems:
1. Check if Node.js and npm are properly installed
2. Ensure network connection is available (required for dependency installation)
3. Check if ports are occupied
4. Check error messages in command line windows 