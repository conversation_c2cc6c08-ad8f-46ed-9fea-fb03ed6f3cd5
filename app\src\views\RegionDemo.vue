<template>
  <div class="region-demo">
    <div class="demo-header">
      <h2>地区数据管理演示</h2>
      <p>展示优化后的地区数据加载和管理功能</p>
    </div>

    <!-- 登录状态显示 -->
    <div class="login-status" :class="{ 'logged-in': userStore.isLogin }">
      <div class="status-icon">
        {{ userStore.isLogin ? '✅' : '❌' }}
      </div>
      <div class="status-text">
        {{ userStore.isLogin ? '已登录' : '未登录' }}
        <span v-if="userStore.isLogin" class="user-name">
          ({{ userStore.userInfo.nickname || userStore.userInfo.username }})
        </span>
      </div>
    </div>

    <!-- 地区数据状态 -->
    <div class="region-status">
      <h3>地区数据状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <label>初始化状态:</label>
          <span :class="{ 'success': regionStore.initialized }">
            {{ regionStore.initialized ? '已初始化' : '未初始化' }}
          </span>
        </div>
        <div class="status-item">
          <label>加载状态:</label>
          <span :class="{ 'loading': regionStore.loading }">
            {{ regionStore.loading ? '加载中...' : '空闲' }}
          </span>
        </div>
        <div class="status-item">
          <label>省份数量:</label>
          <span>{{ regionStore.getAllProvinces.length }}</span>
        </div>
        <div class="status-item">
          <label>缓存城市:</label>
          <span>{{ Object.keys(regionStore.citiesMap).length }} 个省份</span>
        </div>
        <div class="status-item">
          <label>最后更新:</label>
          <span>{{ formatTime(regionStore.lastUpdated) }}</span>
        </div>
        <div class="status-item">
          <label>错误信息:</label>
          <span class="error">{{ regionStore.error || '无' }}</span>
        </div>
      </div>
    </div>

    <!-- 功能演示 -->
    <div class="demo-section" v-if="userStore.isLogin">
      <h3>功能演示</h3>
      
      <!-- 地区选择器 -->
      <div class="demo-item">
        <h4>地区选择器</h4>
        <RegionSelector 
          v-model:province-code="selectedProvince"
          v-model:city-code="selectedCity"
          @change="onRegionChange"
        />
        <div class="selected-info" v-if="selectedProvince || selectedCity">
          <p>选中的地区: {{ formatSelectedRegion() }}</p>
        </div>
      </div>

      <!-- 搜索功能 -->
      <div class="demo-item">
        <h4>地区搜索</h4>
        <input 
          v-model="searchKeyword" 
          placeholder="输入省份或城市名称搜索..."
          class="search-input"
          @input="onSearch"
        />
        <div class="search-results" v-if="searchResults">
          <div class="result-section" v-if="searchResults.provinces.length > 0">
            <h5>匹配的省份:</h5>
            <div class="result-list">
              <span 
                v-for="province in searchResults.provinces" 
                :key="province.code"
                class="result-item province"
                @click="selectProvince(province.code)"
              >
                {{ province.name }}
              </span>
            </div>
          </div>
          <div class="result-section" v-if="searchResults.cities.length > 0">
            <h5>匹配的城市:</h5>
            <div class="result-list">
              <span 
                v-for="city in searchResults.cities" 
                :key="`${city.provinceCode}-${city.code}`"
                class="result-item city"
                @click="selectCity(city.provinceCode, city.code)"
              >
                {{ city.provinceName }} - {{ city.name }}
              </span>
            </div>
          </div>
          <div v-if="searchResults.provinces.length === 0 && searchResults.cities.length === 0" class="no-results">
            没有找到匹配的结果
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="demo-actions">
        <button @click="refreshData" :disabled="regionStore.loading" class="btn primary">
          {{ regionStore.loading ? '刷新中...' : '刷新数据' }}
        </button>
        <button @click="clearData" class="btn secondary">
          清理缓存
        </button>
        <button @click="preloadHotCities" class="btn secondary">
          预加载热门城市
        </button>
      </div>
    </div>

    <!-- 未登录提示 -->
    <div class="login-prompt" v-else>
      <h3>⚠️ 需要登录</h3>
      <p>地区数据功能需要用户登录后才能使用</p>
      <p>请先登录后再试</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUserStore } from '../stores/userStore'
import { useRegionStore } from '../stores/regionStore'
import RegionSelector from '../components/RegionSelector.vue'

const userStore = useUserStore()
const regionStore = useRegionStore()

// 选中的地区
const selectedProvince = ref('')
const selectedCity = ref('')

// 搜索相关
const searchKeyword = ref('')
const searchResults = ref(null)

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '从未更新'
  return new Date(timestamp).toLocaleString()
}

// 格式化选中的地区
const formatSelectedRegion = () => {
  return regionStore.formatRegionString(selectedProvince.value, selectedCity.value)
}

// 地区选择变化
const onRegionChange = (data) => {
  console.log('地区选择变化:', data)
}

// 搜索功能
const onSearch = () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = null
    return
  }
  
  searchResults.value = regionStore.searchRegions(searchKeyword.value)
}

// 选择省份
const selectProvince = (provinceCode) => {
  selectedProvince.value = provinceCode
  selectedCity.value = ''
  searchKeyword.value = ''
  searchResults.value = null
}

// 选择城市
const selectCity = (provinceCode, cityCode) => {
  selectedProvince.value = provinceCode
  selectedCity.value = cityCode
  searchKeyword.value = ''
  searchResults.value = null
}

// 刷新数据
const refreshData = async () => {
  try {
    await regionStore.refreshRegionData()
    console.log('数据刷新完成')
  } catch (error) {
    console.error('数据刷新失败:', error)
  }
}

// 清理数据
const clearData = () => {
  regionStore.clearRegionData()
  selectedProvince.value = ''
  selectedCity.value = ''
  searchKeyword.value = ''
  searchResults.value = null
  console.log('缓存已清理')
}

// 预加载热门城市
const preloadHotCities = async () => {
  try {
    await regionStore.preloadHotCities()
    console.log('热门城市预加载完成')
  } catch (error) {
    console.error('热门城市预加载失败:', error)
  }
}

// 监听搜索关键词变化
watch(searchKeyword, (newValue) => {
  if (!newValue.trim()) {
    searchResults.value = null
  }
})
</script>

<style scoped>
.region-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 14px;
}

.login-status {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
}

.login-status.logged-in {
  background: #d4edda;
  border-color: #c3e6cb;
}

.status-icon {
  font-size: 20px;
  margin-right: 10px;
}

.status-text {
  font-weight: 500;
}

.user-name {
  color: #666;
  font-weight: normal;
}

.region-status {
  margin-bottom: 30px;
}

.region-status h3 {
  margin-bottom: 15px;
  color: #333;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.status-item label {
  font-weight: 500;
  color: #495057;
}

.status-item .success {
  color: #28a745;
}

.status-item .loading {
  color: #007bff;
}

.status-item .error {
  color: #dc3545;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.demo-item {
  margin-bottom: 25px;
  padding: 20px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
}

.demo-item h4 {
  margin-bottom: 15px;
  color: #495057;
}

.selected-info {
  margin-top: 10px;
  padding: 10px;
  background: #e9ecef;
  border-radius: 4px;
}

.search-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.search-results {
  margin-top: 15px;
}

.result-section {
  margin-bottom: 15px;
}

.result-section h5 {
  margin-bottom: 8px;
  color: #495057;
  font-size: 14px;
}

.result-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.result-item {
  padding: 6px 12px;
  background: #e9ecef;
  border-radius: 20px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-item:hover {
  background: #dee2e6;
}

.result-item.province {
  background: #cce5ff;
}

.result-item.city {
  background: #ffe6cc;
}

.no-results {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.demo-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.primary {
  background: #007bff;
  color: white;
}

.btn.primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.secondary:hover {
  background: #545b62;
}

.login-prompt {
  text-align: center;
  padding: 40px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.login-prompt h3 {
  color: #856404;
  margin-bottom: 15px;
}

.login-prompt p {
  color: #856404;
  margin-bottom: 10px;
}
</style>
