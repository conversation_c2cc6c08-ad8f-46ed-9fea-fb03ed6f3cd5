@echo off
echo Starting Full Application (Frontend + Backend)...
echo.

echo Starting backend server...
start "Backend Server" cmd /k "%~dp0start-backend.bat"

echo Waiting for backend to start...
timeout /t 3 /nobreak >nul

echo Starting frontend application...
start "Frontend App" cmd /k "%~dp0start-frontend.bat"

echo.
echo Both services have been started:
echo - Backend server is running
echo - Frontend application is running
echo.
echo Closing this window will not stop the services
echo Please press Ctrl+C in each window to stop the services
echo.

rem pause 
