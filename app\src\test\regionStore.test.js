/**
 * 地区数据Store测试
 * 验证地区数据的加载和缓存功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useRegionStore } from '../stores/regionStore'
import { userService } from '../services/userService'

// Mock userService
vi.mock('../services/userService', () => ({
  userService: {
    getRegions: vi.fn()
  }
}))

describe('RegionStore', () => {
  let regionStore

  beforeEach(() => {
    // 创建新的pinia实例
    setActivePinia(createPinia())
    regionStore = useRegionStore()
    
    // 重置mock
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(regionStore.provinces).toEqual([])
      expect(regionStore.citiesMap).toEqual({})
      expect(regionStore.loading).toBe(false)
      expect(regionStore.error).toBe(null)
      expect(regionStore.initialized).toBe(false)
      expect(regionStore.lastUpdated).toBe(null)
    })
  })

  describe('getters', () => {
    beforeEach(() => {
      // 设置测试数据
      regionStore.provinces = [
        { code: '11', name: '北京市' },
        { code: '31', name: '上海市' }
      ]
      regionStore.citiesMap = {
        '11': [
          { code: '1101', name: '东城区' },
          { code: '1102', name: '西城区' }
        ],
        '31': [
          { code: '3101', name: '黄浦区' },
          { code: '3102', name: '徐汇区' }
        ]
      }
    })

    it('getAllProvinces 应该返回所有省份', () => {
      expect(regionStore.getAllProvinces).toEqual([
        { code: '11', name: '北京市' },
        { code: '31', name: '上海市' }
      ])
    })

    it('getCitiesByProvince 应该返回指定省份的城市', () => {
      expect(regionStore.getCitiesByProvince('11')).toEqual([
        { code: '1101', name: '东城区' },
        { code: '1102', name: '西城区' }
      ])
      expect(regionStore.getCitiesByProvince('31')).toEqual([
        { code: '3101', name: '黄浦区' },
        { code: '3102', name: '徐汇区' }
      ])
      expect(regionStore.getCitiesByProvince('99')).toEqual([])
    })

    it('getProvinceName 应该返回省份名称', () => {
      expect(regionStore.getProvinceName('11')).toBe('北京市')
      expect(regionStore.getProvinceName('31')).toBe('上海市')
      expect(regionStore.getProvinceName('99')).toBe('')
    })

    it('getCityName 应该返回城市名称', () => {
      expect(regionStore.getCityName('11', '1101')).toBe('东城区')
      expect(regionStore.getCityName('31', '3101')).toBe('黄浦区')
      expect(regionStore.getCityName('11', '9999')).toBe('')
      expect(regionStore.getCityName('99', '1101')).toBe('')
    })

    it('formatRegionString 应该格式化地区字符串', () => {
      expect(regionStore.formatRegionString('11', '1101')).toBe('北京市 东城区')
      expect(regionStore.formatRegionString('31', '3101')).toBe('上海市 黄浦区')
      expect(regionStore.formatRegionString('11', '')).toBe('北京市')
      expect(regionStore.formatRegionString('', '1101')).toBe('')
    })

    it('needsRefresh 应该正确判断是否需要刷新', () => {
      // 没有更新时间，需要刷新
      expect(regionStore.needsRefresh).toBe(true)
      
      // 刚更新，不需要刷新
      regionStore.lastUpdated = Date.now()
      expect(regionStore.needsRefresh).toBe(false)
      
      // 超过1小时，需要刷新
      regionStore.lastUpdated = Date.now() - 2 * 60 * 60 * 1000
      expect(regionStore.needsRefresh).toBe(true)
    })
  })

  describe('actions', () => {
    describe('initRegionData', () => {
      it('应该成功初始化地区数据', async () => {
        const mockData = {
          provinces: [{ code: '11', name: '北京市' }],
          cities: { '11': [{ code: '1101', name: '东城区' }] }
        }
        
        userService.getRegions.mockResolvedValue({ data: mockData })

        await regionStore.initRegionData()

        expect(regionStore.provinces).toEqual(mockData.provinces)
        expect(regionStore.citiesMap).toEqual(mockData.cities)
        expect(regionStore.initialized).toBe(true)
        expect(regionStore.lastUpdated).toBeTypeOf('number')
        expect(regionStore.loading).toBe(false)
        expect(regionStore.error).toBe(null)
      })

      it('应该处理API错误', async () => {
        const errorMessage = '网络错误'
        userService.getRegions.mockRejectedValue(new Error(errorMessage))

        await expect(regionStore.initRegionData()).rejects.toThrow(errorMessage)
        
        expect(regionStore.error).toBe(errorMessage)
        expect(regionStore.loading).toBe(false)
        expect(regionStore.initialized).toBe(false)
      })

      it('如果已初始化且不需要刷新，应该直接返回', async () => {
        regionStore.initialized = true
        regionStore.lastUpdated = Date.now()

        await regionStore.initRegionData()

        expect(userService.getRegions).not.toHaveBeenCalled()
      })
    })

    describe('loadCities', () => {
      it('应该成功加载城市数据', async () => {
        const mockCities = [
          { code: '1101', name: '东城区' },
          { code: '1102', name: '西城区' }
        ]
        
        userService.getRegions.mockResolvedValue(mockCities)

        const result = await regionStore.loadCities('11')

        expect(result).toEqual(mockCities)
        expect(regionStore.citiesMap['11']).toEqual(mockCities)
        expect(userService.getRegions).toHaveBeenCalledWith('11')
      })

      it('如果有缓存，应该直接返回缓存数据', async () => {
        const cachedCities = [{ code: '1101', name: '东城区' }]
        regionStore.citiesMap['11'] = cachedCities

        const result = await regionStore.loadCities('11')

        expect(result).toEqual(cachedCities)
        expect(userService.getRegions).not.toHaveBeenCalled()
      })

      it('如果省份代码为空，应该返回空数组', async () => {
        const result = await regionStore.loadCities('')
        expect(result).toEqual([])
        expect(userService.getRegions).not.toHaveBeenCalled()
      })

      it('应该处理API错误', async () => {
        userService.getRegions.mockRejectedValue(new Error('网络错误'))

        const result = await regionStore.loadCities('11')

        expect(result).toEqual([])
      })
    })

    describe('clearRegionData', () => {
      it('应该清理所有数据', () => {
        // 设置一些数据
        regionStore.provinces = [{ code: '11', name: '北京市' }]
        regionStore.citiesMap = { '11': [] }
        regionStore.initialized = true
        regionStore.lastUpdated = Date.now()
        regionStore.error = '错误'

        regionStore.clearRegionData()

        expect(regionStore.provinces).toEqual([])
        expect(regionStore.citiesMap).toEqual({})
        expect(regionStore.initialized).toBe(false)
        expect(regionStore.lastUpdated).toBe(null)
        expect(regionStore.error).toBe(null)
      })
    })

    describe('searchRegions', () => {
      beforeEach(() => {
        regionStore.provinces = [
          { code: '11', name: '北京市' },
          { code: '31', name: '上海市' },
          { code: '44', name: '广东省' }
        ]
        regionStore.citiesMap = {
          '11': [{ code: '1101', name: '东城区' }],
          '31': [{ code: '3101', name: '黄浦区' }],
          '44': [
            { code: '4401', name: '广州市' },
            { code: '4403', name: '深圳市' }
          ]
        }
      })

      it('应该搜索省份', () => {
        const result = regionStore.searchRegions('北京')
        
        expect(result.provinces).toEqual([
          { code: '11', name: '北京市' }
        ])
      })

      it('应该搜索城市', () => {
        const result = regionStore.searchRegions('广州')
        
        expect(result.cities).toEqual([
          { code: '4401', name: '广州市', provinceName: '广东省', provinceCode: '44' }
        ])
      })

      it('空关键词应该返回空结果', () => {
        const result = regionStore.searchRegions('')
        
        expect(result.provinces).toEqual([])
        expect(result.cities).toEqual([])
      })
    })
  })
})
