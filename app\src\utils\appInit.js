/**
 * 应用初始化工具
 * 负责管理应用启动时的各种初始化任务
 */

import { useUserStore } from '../stores/userStore'
import { useRegionStore } from '../stores/regionStore'
import { useMessageStore } from '../stores/messageStore'
import config from '../config'

/**
 * 应用初始化管理器
 */
class AppInitializer {
  constructor() {
    this.initialized = false
    this.initPromise = null
    this.userDataInitialized = false
    this.stores = {
      user: null,
      region: null,
      message: null
    }
  }

  /**
   * 初始化stores
   */
  initStores() {
    this.stores.user = useUserStore()
    this.stores.region = useRegionStore()
    this.stores.message = useMessageStore()
  }

  /**
   * 检查用户登录状态（仅在应用启动时调用）
   */
  async initUserSession() {
    const userStore = this.stores.user

    if (!userStore.isLogin) {
      console.log('👤 用户未登录，跳过用户数据初始化')
      return false
    }

    // 如果用户已登录，标记需要初始化，但不在这里执行
    // 让 handleUserLogin 方法来处理
    console.log('👤 检测到用户已登录，等待登录事件触发初始化')
    return true
  }

  /**
   * 处理用户登录事件（由 App.vue 调用）
   * 这个方法独立于应用初始化，可以在任何时候调用
   */
  async handleUserLogin() {
    try {
      // 动态导入 stores，确保它们可用
      const { useUserStore } = await import('../stores/userStore')
      const { useRegionStore } = await import('../stores/regionStore')
      const { useMessageStore } = await import('../stores/messageStore')

      const userStore = useUserStore()
      const regionStore = useRegionStore()
      const messageStore = useMessageStore()

      if (this.userDataInitialized) {
        console.log('👤 用户数据已初始化，跳过重复初始化')
        return true
      }

      // 检查用户是否已登录
      if (!userStore || !userStore.isLogin) {
        console.log('👤 用户未登录，无法初始化用户数据')
        return false
      }

      // 检查用户信息是否完整
      if (!userStore.userInfo || !userStore.userInfo.id) {
        console.error('❌ 用户信息不完整，无法初始化')
        return false
      }

      console.log('👤 用户登录，开始初始化用户数据...')

      // 初始化消息系统
      const userData = {
        id: userStore.userInfo.id,
        name: userStore.userInfo.nickname || userStore.userInfo.username,
        avatar: userStore.userInfo.avatar,
        token: userStore.token
      }

      // 初始化消息系统
      if (messageStore) {
        messageStore.init(userData)

        // 异步初始化消息数据（不阻塞主流程）
        messageStore.initData().catch(error => {
          console.warn('⚠️ 消息数据初始化失败:', error)
        })
      }

      // 初始化地区数据
      if (regionStore) {
        await regionStore.initRegionData()
      }

      this.userDataInitialized = true
      console.log('✅ 用户数据初始化完成')
      return true

    } catch (error) {
      console.error('❌ 用户数据初始化失败:', error)
      // 重置状态，允许重试
      this.userDataInitialized = false
      return false
    }
  }

  /**
   * 初始化地区数据
   */
  async initRegionData() {
    try {
      console.log('🌍 开始初始化地区数据...')
      
      // 初始化基础地区数据
      await this.stores.region.initRegionData()
      
      // 异步预加载热门城市数据（不阻塞主流程）
      this.stores.region.preloadHotCities().catch(error => {
        console.warn('⚠️ 热门城市数据预加载失败:', error)
      })
      
      console.log('✅ 地区数据初始化完成')
      
    } catch (error) {
      console.warn('⚠️ 地区数据初始化失败:', error)
      // 地区数据初始化失败不应该阻塞应用启动
    }
  }

  /**
   * 设置全局样式和主题
   */
  setupGlobalStyles() {
    try {
      // 设置CSS变量
      const root = document.documentElement
      root.style.setProperty('--primary-color', '#ff6b6b')
      root.style.setProperty('--secondary-color', '#4ecdc4')
      root.style.setProperty('--success-color', '#51cf66')
      root.style.setProperty('--warning-color', '#ffd43b')
      root.style.setProperty('--error-color', '#ff6b6b')
      root.style.setProperty('--info-color', '#74c0fc')
      
      // 设置应用标题
      document.title = config.APP_TITLE
      
      // 设置viewport meta标签
      let viewport = document.querySelector('meta[name="viewport"]')
      if (!viewport) {
        viewport = document.createElement('meta')
        viewport.name = 'viewport'
        document.head.appendChild(viewport)
      }
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      
      console.log('🎨 全局样式设置完成')
      
    } catch (error) {
      console.error('❌ 全局样式设置失败:', error)
    }
  }

  /**
   * 设置全局错误处理
   */
  setupErrorHandling() {
    // 全局未捕获错误处理
    window.addEventListener('error', (event) => {
      console.error('全局错误:', event.error)
      
      // 在开发环境下显示更多信息
      if (config.ENV === 'development') {
        console.error('错误详情:', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        })
      }
    })

    // 全局Promise拒绝处理
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise拒绝:', event.reason)
      
      // 阻止默认的控制台错误输出
      event.preventDefault()
    })

    console.log('🛡️ 全局错误处理设置完成')
  }

  /**
   * 打印启动信息
   */
  printStartupInfo() {
    if (config.ENV === 'development') {
      console.log(`🚀 ${config.APP_TITLE} 启动成功`)
      console.log(`📦 环境: ${config.ENV}`)
      console.log(`🔗 API地址: ${config.API_BASE_URL}`)
      console.log(`📡 WebSocket: ${config.WS_URL}`)
      console.log(`🖼️ 图片地址: ${config.IMAGE_BASE_URL}`)
      console.log('🛠️ 开发模式已启用')
    } else {
      console.log(`🚀 ${config.APP_TITLE} 启动成功`)
    }
  }

  /**
   * 主初始化方法
   */
  async initialize() {
    // 如果已经初始化过，返回之前的Promise
    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._doInitialize()
    return this.initPromise
  }

  /**
   * 执行初始化
   */
  async _doInitialize() {
    if (this.initialized) {
      return true
    }

    try {
      console.log('🔄 开始应用初始化...')

      // 1. 初始化stores
      this.initStores()

      // 2. 设置全局样式
      this.setupGlobalStyles()

      // 3. 设置错误处理
      this.setupErrorHandling()

      // 4. 初始化用户会话
      await this.initUserSession()

      // 5. 打印启动信息
      this.printStartupInfo()

      this.initialized = true
      console.log('✅ 应用初始化完成')
      
      return true

    } catch (error) {
      console.error('❌ 应用初始化失败:', error)
      return false
    }
  }

  /**
   * 处理用户登出事件
   */
  handleUserLogout() {
    this.userDataInitialized = false
    console.log('👋 用户登出，重置用户数据初始化状态')
  }

  /**
   * 重置初始化状态（用于测试或重新初始化）
   */
  reset() {
    this.initialized = false
    this.initPromise = null
    this.userDataInitialized = false
    console.log('🔄 应用初始化状态已重置')
  }

  /**
   * 清理资源
   */
  cleanup() {
    try {
      // 清理消息连接
      if (this.stores.message) {
        this.stores.message.disconnectSocket()
      }

      // 清理地区数据
      if (this.stores.region) {
        this.stores.region.clearRegionData()
      }

      console.log('🧹 应用资源清理完成')
    } catch (error) {
      console.error('❌ 应用资源清理失败:', error)
    }
  }
}

// 创建全局实例
const appInitializer = new AppInitializer()

export default appInitializer

// 导出便捷方法
export const initializeApp = () => appInitializer.initialize()
export const resetApp = () => appInitializer.reset()
export const cleanupApp = () => appInitializer.cleanup()
